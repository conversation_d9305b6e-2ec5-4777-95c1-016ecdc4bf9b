import React, { forwardRef } from 'react';

// Enhanced TypeScript interfaces
interface CardVariantConfig {
  base: string;
  hover: string;
  focus: string;
  shadow: string;
  border: string;
}

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'neural' | 'quantum' | 'cellular' | 'consciousness' | 'creativity' | 'intuition';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  elevation?: 'none' | 'subtle' | 'medium' | 'high' | 'dramatic';
  interactive?: boolean;
  disabled?: boolean;
  loading?: boolean;
  customGradient?: string;
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  onHover?: (isHovered: boolean) => void;
}

// Professional design system configuration
const CARD_VARIANTS: Record<string, CardVariantConfig> = {
  neural: {
    base: 'bg-gradient-to-br from-slate-50/80 via-white/90 to-blue-50/80 dark:from-slate-900/80 dark:via-slate-800/90 dark:to-blue-950/80',
    hover: 'hover:from-slate-100/90 hover:via-white/95 hover:to-blue-100/90 dark:hover:from-slate-800/90 dark:hover:via-slate-700/95 dark:hover:to-blue-900/90',
    focus: 'focus-visible:ring-2 focus-visible:ring-blue-500/50 focus-visible:ring-offset-2 dark:focus-visible:ring-blue-400/50',
    shadow: 'shadow-lg shadow-blue-500/10 dark:shadow-blue-400/20',
    border: 'border border-slate-200/60 dark:border-slate-700/60'
  },
  quantum: {
    base: 'bg-gradient-to-br from-violet-50/80 via-white/90 to-purple-50/80 dark:from-violet-950/80 dark:via-slate-900/90 dark:to-purple-950/80',
    hover: 'hover:from-violet-100/90 hover:via-white/95 hover:to-purple-100/90 dark:hover:from-violet-900/90 dark:hover:via-slate-800/95 dark:hover:to-purple-900/90',
    focus: 'focus-visible:ring-2 focus-visible:ring-violet-500/50 focus-visible:ring-offset-2 dark:focus-visible:ring-violet-400/50',
    shadow: 'shadow-lg shadow-violet-500/15 dark:shadow-violet-400/25',
    border: 'border border-violet-200/60 dark:border-violet-700/60'
  },
  cellular: {
    base: 'bg-gradient-to-br from-emerald-50/80 via-white/90 to-teal-50/80 dark:from-emerald-950/80 dark:via-slate-900/90 dark:to-teal-950/80',
    hover: 'hover:from-emerald-100/90 hover:via-white/95 hover:to-teal-100/90 dark:hover:from-emerald-900/90 dark:hover:via-slate-800/95 dark:hover:to-teal-900/90',
    focus: 'focus-visible:ring-2 focus-visible:ring-emerald-500/50 focus-visible:ring-offset-2 dark:focus-visible:ring-emerald-400/50',
    shadow: 'shadow-lg shadow-emerald-500/15 dark:shadow-emerald-400/25',
    border: 'border border-emerald-200/60 dark:border-emerald-700/60'
  },
  consciousness: {
    base: 'bg-gradient-to-br from-amber-50/80 via-white/90 to-orange-50/80 dark:from-amber-950/80 dark:via-slate-900/90 dark:to-orange-950/80',
    hover: 'hover:from-amber-100/90 hover:via-white/95 hover:to-orange-100/90 dark:hover:from-amber-900/90 dark:hover:via-slate-800/95 dark:hover:to-orange-900/90',
    focus: 'focus-visible:ring-2 focus-visible:ring-amber-500/50 focus-visible:ring-offset-2 dark:focus-visible:ring-amber-400/50',
    shadow: 'shadow-lg shadow-amber-500/15 dark:shadow-amber-400/25',
    border: 'border border-amber-200/60 dark:border-amber-700/60'
  },
  creativity: {
    base: 'bg-gradient-to-br from-cyan-50/80 via-white/90 to-sky-50/80 dark:from-cyan-950/80 dark:via-slate-900/90 dark:to-sky-950/80',
    hover: 'hover:from-cyan-100/90 hover:via-white/95 hover:to-sky-100/90 dark:hover:from-cyan-900/90 dark:hover:via-slate-800/95 dark:hover:to-sky-900/90',
    focus: 'focus-visible:ring-2 focus-visible:ring-cyan-500/50 focus-visible:ring-offset-2 dark:focus-visible:ring-cyan-400/50',
    shadow: 'shadow-lg shadow-cyan-500/15 dark:shadow-cyan-400/25',
    border: 'border border-cyan-200/60 dark:border-cyan-700/60'
  },
  intuition: {
    base: 'bg-gradient-to-br from-pink-50/80 via-white/90 to-rose-50/80 dark:from-pink-950/80 dark:via-slate-900/90 dark:to-rose-950/80',
    hover: 'hover:from-pink-100/90 hover:via-white/95 hover:to-rose-100/90 dark:hover:from-pink-900/90 dark:hover:via-slate-800/95 dark:hover:to-rose-900/90',
    focus: 'focus-visible:ring-2 focus-visible:ring-pink-500/50 focus-visible:ring-offset-2 dark:focus-visible:ring-pink-400/50',
    shadow: 'shadow-lg shadow-pink-500/15 dark:shadow-pink-400/25',
    border: 'border border-pink-200/60 dark:border-pink-700/60'
  }
};

const CARD_SIZES = {
  xs: 'p-3 rounded-lg',
  sm: 'p-4 rounded-lg',
  md: 'p-6 rounded-xl',
  lg: 'p-8 rounded-xl',
  xl: 'p-10 rounded-2xl'
};

const CARD_ELEVATIONS = {
  none: '',
  subtle: 'shadow-sm',
  medium: 'shadow-md',
  high: 'shadow-xl',
  dramatic: 'shadow-2xl'
};

// Utility function to combine class names (simplified version of cn)
const cn = (...classes: (string | undefined | null | false)[]): string => {
  return classes.filter(Boolean).join(' ');
};

const Card = forwardRef<HTMLDivElement, CardProps>(({
  variant = 'neural',
  size = 'md',
  elevation = 'medium',
  interactive = false,
  disabled = false,
  loading = false,
  customGradient,
  children,
  className,
  onClick,
  onHover,
  style,
  ...props
}, ref) => {
  const [isHovered, setIsHovered] = React.useState(false);

  const variantConfig = CARD_VARIANTS[variant] || CARD_VARIANTS.neural;
  
  const handleMouseEnter = () => {
    if (!disabled) {
      setIsHovered(true);
      onHover?.(true);
    }
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    onHover?.(false);
  };

  const handleClick = () => {
    if (!disabled && !loading && onClick) {
      onClick();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if ((e.key === 'Enter' || e.key === ' ') && !disabled && !loading && onClick) {
      e.preventDefault();
      onClick();
    }
  };

  // Base styles that always apply
  const baseStyles = cn(
    'relative overflow-hidden transition-all duration-300 ease-out',
    'backdrop-blur-sm backdrop-saturate-150',
    CARD_SIZES[size],
    CARD_ELEVATIONS[elevation]
  );

  // Variant-specific styles
  const variantStyles = cn(
    variantConfig.base,
    variantConfig.border,
    variantConfig.shadow,
    !disabled && variantConfig.hover,
    !disabled && variantConfig.focus
  );

  // Interactive styles
  const interactiveStyles = cn(
    (interactive || onClick) && !disabled && [
      'cursor-pointer',
      'transform-gpu',
      'hover:scale-[1.02]',
      'hover:-translate-y-0.5',
      'active:scale-[0.98]',
      'active:translate-y-0'
    ]
  );

  // State-specific styles
  const stateStyles = cn(
    disabled && 'opacity-60 cursor-not-allowed',
    loading && 'pointer-events-none'
  );

  const finalStyle = customGradient 
    ? { ...style, background: customGradient }
    : style;

  return (
    <div
      ref={ref}
      className={cn(
        baseStyles,
        variantStyles,
        interactiveStyles,
        stateStyles,
        className
      )}
      style={finalStyle}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onKeyDown={(interactive || onClick) ? handleKeyDown : undefined}
      tabIndex={(interactive || onClick) && !disabled ? 0 : undefined}
      role={(interactive || onClick) ? 'button' : undefined}
      aria-disabled={disabled}
      aria-busy={loading}
      {...props}
    >
      {/* Subtle shimmer effect for premium feel */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out" />
      
      {/* Loading state */}
      {loading && (
        <div className="absolute inset-0 bg-white/20 dark:bg-black/20 flex items-center justify-center backdrop-blur-sm rounded-inherit">
          <div className="w-6 h-6 border-2 border-current border-t-transparent rounded-full animate-spin opacity-60" />
        </div>
      )}
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
      
      {/* Glow effect on hover for premium variants */}
      {isHovered && !disabled && (
        <div className={cn(
          "absolute inset-0 rounded-inherit opacity-0 animate-pulse",
          variant === 'quantum' && "bg-gradient-to-r from-violet-400/10 to-purple-400/10",
          variant === 'neural' && "bg-gradient-to-r from-blue-400/10 to-indigo-400/10",
          variant === 'cellular' && "bg-gradient-to-r from-emerald-400/10 to-teal-400/10",
          variant === 'consciousness' && "bg-gradient-to-r from-amber-400/10 to-orange-400/10",
          variant === 'creativity' && "bg-gradient-to-r from-cyan-400/10 to-sky-400/10",
          variant === 'intuition' && "bg-gradient-to-r from-pink-400/10 to-rose-400/10"
        )} />
      )}
    </div>
  );
});

Card.displayName = 'Card';


export default Card;
